<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\User\Components\DataGrid;

use App\Model\Orm\Admin\Admin;
use App\Model\Orm\Orm;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\Orm\User\UserRepository;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\DbalCollection;

class DataGrid extends Control
{

	use HasMutationColumn;

	public function __construct(
		private readonly Translator $translator,
		private readonly Orm $orm,
		private readonly UserRepository $userRepository,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->render(__DIR__ . '/dataGrid.latte');
	}


	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$roles = [
			null => $this->translator->translate('filter_user_all'),
		];
		$roles = array_merge($roles, User::getConstsByPrefix('ROLE_'));
		$statusOptions = User::getConstsByPrefix('STATUS_');

		$grid = new \Ublaboo\DataGrid\DataGrid();
		$users = $this->userRepository->findBy([]);
		$grid->setTranslator($this->translator);

		$grid->setDataSource($users);
		$grid->addColumnText('email', 'email')->setSortable()->setFilterText();
		$grid->addColumnText('lastname', 'lastname')->setSortable()->setFilterText();
		$grid->addColumnText('firstname', 'firstname')->setSortable()->setFilterText();

		$grid->addColumnText('role', 'role')
			->setRenderer(function (User $user) { return $this->translator->translate($user->role);})
			->setFilterSelect($roles)->setPrompt('all')->setTranslateOptions();
		$this->addSubscriptionInfo($grid);

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setItemsPerPageList([30, 50, 100, 150], false);

		$grid->setDefaultSort(['createdTime' => 'DESC']);

		$grid->addExportCsv('Csv export (filtered)', 'users.csv', 'UTF-8', ';', false, true);
		$grid->addExportCsv('Csv export (filtered) for windows', 'users.csv', 'windows-1250', ';', false, true);

		return $grid;
	}

	private function addSubscriptionInfo(\Ublaboo\DataGrid\DataGrid $grid): void
	{
		foreach ($this->orm->mutation->findAll() as $mutation) {
			$name = 'subscribed_' . $mutation->langCode;
			$grid->addColumnText($name, $name)
				->setRenderer(function (User $user) use ($mutation): string {
					$isSubscribed = $user->userMutations->toCollection()->getBy([
						'newsletter' => 1,
						'mutation' => $mutation,
					]);
					return ($isSubscribed !== null) ? 'Ano' : 'Ne';
				})
				->setFilterSelect([1 => 'Ano', 0 => 'Ne'])->setPrompt('vše')
				->setCondition(function (DbalCollection $collection, int|string $value) use ($mutation): void {
					$alias = $mutation->langCode . '_um';
					$collection->getQueryBuilder()
						->joinLeft('[user_mutation] as ' . $alias, '[user.id] = ['.$alias.'.userId]')
						->andWhere('%s.newsletter = %i', $alias, (int)$value)
						->andWhere('%s.mutationId = %i', $alias, $mutation->id);
				});
		}
	}

}
